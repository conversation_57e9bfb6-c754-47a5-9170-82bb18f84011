@echo off
echo ========================================
echo   Difference Feature GUI - Fitur Pembolong
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python tidak ditemukan!
    echo Pastikan Python sudah terinstall dan ada di PATH
    pause
    exit /b 1
)

echo Python ditemukan, memeriksa dependencies...

REM Check and install required packages
echo Memeriksa GeoPandas...
python -c "import geopandas" >nul 2>&1
if errorlevel 1 (
    echo Installing GeoPandas...
    pip install geopandas
)

echo Memeriksa Pandas...
python -c "import pandas" >nul 2>&1
if errorlevel 1 (
    echo Installing Pandas...
    pip install pandas
)

echo.
echo Menjalankan Difference Feature GUI...
echo.

REM Run the application
python difference_feature_gui.py

if errorlevel 1 (
    echo.
    echo ERROR: <PERSON><PERSON><PERSON><PERSON> k<PERSON> saat menjalankan program
    echo Periksa log file untuk detail error
    pause
) else (
    echo.
    echo Program selesai dijalankan
)

pause
