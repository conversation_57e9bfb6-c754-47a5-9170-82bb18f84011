#!/usr/bin/env python3
"""
Difference Feature GUI - Fitur Pembolong Berdasarkan 2 Input Layer
Aplikasi GUI untuk melakukan operasi spatial difference antara dua layer shapefile.

Author: Augment Agent
Date: 2025-07-05
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import geopandas as gpd
import pandas as pd
from pathlib import Path
import os
from datetime import datetime
import logging

class DifferenceFeatureGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Difference Feature GUI - Fitur Pembolong")
        self.root.geometry("900x700")
        
        # Data storage
        self.gdf_base = None
        self.gdf_cutting = None
        self.base_features = []
        self.cutting_features = []
        
        # Setup logging
        self.setup_logging()
        
        # Create GUI
        self.create_widgets()
        
    def setup_logging(self):
        """Setup logging configuration"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / f"difference_feature_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def create_widgets(self):
        """Create main GUI widgets"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Fitur Pembolong - Spatial Difference Tool", 
                               font=('Arial', 14, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Base Layer Section
        self.create_base_layer_section(main_frame, row=1)
        
        # Cutting Layer Section  
        self.create_cutting_layer_section(main_frame, row=2)
        
        # Feature Selection Section
        self.create_feature_selection_section(main_frame, row=3)
        
        # Output Section
        self.create_output_section(main_frame, row=4)
        
        # Control Buttons
        self.create_control_buttons(main_frame, row=5)
        
        # Progress and Status
        self.create_progress_section(main_frame, row=6)
        
        # Log Section
        self.create_log_section(main_frame, row=7)
        
    def create_base_layer_section(self, parent, row):
        """Create base layer input section"""
        frame = ttk.LabelFrame(parent, text="Base Layer (Layer yang akan dipotong)", padding="5")
        frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        frame.columnconfigure(1, weight=1)
        
        ttk.Label(frame, text="File:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.base_path_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.base_path_var, state='readonly').grid(
            row=0, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(frame, text="Browse", command=self.browse_base_layer).grid(
            row=0, column=2, padx=(5, 0))
            
        # Base layer info
        self.base_info_var = tk.StringVar(value="No file selected")
        ttk.Label(frame, textvariable=self.base_info_var, foreground="blue").grid(
            row=1, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))
    
    def create_cutting_layer_section(self, parent, row):
        """Create cutting layer input section"""
        frame = ttk.LabelFrame(parent, text="Cutting Layer (Layer pemotong)", padding="5")
        frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        frame.columnconfigure(1, weight=1)
        
        ttk.Label(frame, text="File:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.cutting_path_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.cutting_path_var, state='readonly').grid(
            row=0, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(frame, text="Browse", command=self.browse_cutting_layer).grid(
            row=0, column=2, padx=(5, 0))
            
        # Cutting layer info
        self.cutting_info_var = tk.StringVar(value="No file selected")
        ttk.Label(frame, textvariable=self.cutting_info_var, foreground="blue").grid(
            row=1, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))
    
    def create_feature_selection_section(self, parent, row):
        """Create feature selection section"""
        frame = ttk.LabelFrame(parent, text="Feature Selection", padding="5")
        frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        frame.columnconfigure(0, weight=1)
        frame.columnconfigure(1, weight=1)
        parent.rowconfigure(row, weight=1)
        
        # Base layer features
        base_frame = ttk.LabelFrame(frame, text="Base Layer Features (HCV_Catego = 0)", padding="5")
        base_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        base_frame.columnconfigure(0, weight=1)
        base_frame.rowconfigure(1, weight=1)
        
        self.base_listbox = tk.Listbox(base_frame, selectmode=tk.MULTIPLE, height=8)
        self.base_listbox.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        base_scroll = ttk.Scrollbar(base_frame, orient=tk.VERTICAL, command=self.base_listbox.yview)
        base_scroll.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.base_listbox.configure(yscrollcommand=base_scroll.set)
        
        # Cutting layer features
        cutting_frame = ttk.LabelFrame(frame, text="Cutting Layer Features (HCV_Catego = 0)", padding="5")
        cutting_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        cutting_frame.columnconfigure(0, weight=1)
        cutting_frame.rowconfigure(1, weight=1)
        
        self.cutting_listbox = tk.Listbox(cutting_frame, selectmode=tk.MULTIPLE, height=8)
        self.cutting_listbox.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        cutting_scroll = ttk.Scrollbar(cutting_frame, orient=tk.VERTICAL, command=self.cutting_listbox.yview)
        cutting_scroll.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.cutting_listbox.configure(yscrollcommand=cutting_scroll.set)
    
    def create_output_section(self, parent, row):
        """Create output section"""
        frame = ttk.LabelFrame(parent, text="Output", padding="5")
        frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        frame.columnconfigure(1, weight=1)
        
        ttk.Label(frame, text="Output File:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.output_path_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.output_path_var).grid(
            row=0, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(frame, text="Browse", command=self.browse_output_file).grid(
            row=0, column=2, padx=(5, 0))
    
    def create_control_buttons(self, parent, row):
        """Create control buttons"""
        frame = ttk.Frame(parent)
        frame.grid(row=row, column=0, columnspan=3, pady=10)
        
        ttk.Button(frame, text="Load Layers", command=self.load_layers).pack(side=tk.LEFT, padx=5)
        ttk.Button(frame, text="Process Difference", command=self.process_difference).pack(side=tk.LEFT, padx=5)
        ttk.Button(frame, text="Clear All", command=self.clear_all).pack(side=tk.LEFT, padx=5)
        ttk.Button(frame, text="Exit", command=self.root.quit).pack(side=tk.LEFT, padx=5)
    
    def create_progress_section(self, parent, row):
        """Create progress section"""
        frame = ttk.Frame(parent)
        frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        frame.columnconfigure(0, weight=1)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        self.status_var = tk.StringVar(value="Ready")
        ttk.Label(frame, textvariable=self.status_var).grid(row=0, column=1)
    
    def create_log_section(self, parent, row):
        """Create log section"""
        frame = ttk.LabelFrame(parent, text="Log", padding="5")
        frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        frame.columnconfigure(0, weight=1)
        frame.rowconfigure(0, weight=1)
        parent.rowconfigure(row, weight=1)
        
        self.log_text = tk.Text(frame, height=8, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        log_scroll = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scroll.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=log_scroll.set)
    
    def log(self, message):
        """Add message to log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        self.logger.info(message)
    
    def browse_base_layer(self):
        """Browse for base layer shapefile"""
        file_path = filedialog.askopenfilename(
            title="Select Base Layer Shapefile",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")]
        )
        if file_path:
            self.base_path_var.set(file_path)
            self.log(f"Base layer selected: {os.path.basename(file_path)}")
    
    def browse_cutting_layer(self):
        """Browse for cutting layer shapefile"""
        file_path = filedialog.askopenfilename(
            title="Select Cutting Layer Shapefile", 
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")]
        )
        if file_path:
            self.cutting_path_var.set(file_path)
            self.log(f"Cutting layer selected: {os.path.basename(file_path)}")
    
    def browse_output_file(self):
        """Browse for output file location"""
        file_path = filedialog.asksaveasfilename(
            title="Save Output Shapefile As",
            defaultextension=".shp",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")]
        )
        if file_path:
            self.output_path_var.set(file_path)
            self.log(f"Output file set: {os.path.basename(file_path)}")

    def load_layers(self):
        """Load both layers and populate feature lists"""
        try:
            if not self.base_path_var.get() or not self.cutting_path_var.get():
                messagebox.showerror("Error", "Please select both base and cutting layer files")
                return

            self.status_var.set("Loading layers...")
            self.progress_var.set(10)

            # Load base layer
            self.log("Loading base layer...")
            self.gdf_base = gpd.read_file(self.base_path_var.get())
            self.log(f"Base layer loaded: {len(self.gdf_base)} features")

            # Standardize columns
            self.standardize_columns(self.gdf_base, "base")

            self.progress_var.set(30)

            # Load cutting layer
            self.log("Loading cutting layer...")
            self.gdf_cutting = gpd.read_file(self.cutting_path_var.get())
            self.log(f"Cutting layer loaded: {len(self.gdf_cutting)} features")

            # Standardize columns
            self.standardize_columns(self.gdf_cutting, "cutting")

            self.progress_var.set(50)

            # Check CRS compatibility
            self.check_crs_compatibility()

            self.progress_var.set(70)

            # Populate feature lists
            self.populate_feature_lists()

            self.progress_var.set(100)
            self.status_var.set("Layers loaded successfully")

            # Update info labels
            self.update_layer_info()

        except Exception as e:
            self.log(f"Error loading layers: {str(e)}")
            messagebox.showerror("Error", f"Failed to load layers: {str(e)}")
            self.status_var.set("Error loading layers")
            self.progress_var.set(0)

    def standardize_columns(self, gdf, layer_name):
        """Standardize column names"""
        column_mappings = {
            'SUB_DIVISI': 'SUBDIVISI',
            'HCV_Catego': 'HCV_Catego',
            'Jumlah_Poh': 'JUMLAH_POH'
        }

        for old, new in column_mappings.items():
            if old in gdf.columns and new not in gdf.columns:
                gdf.rename(columns={old: new}, inplace=True)
                self.log(f"Renamed column {old} to {new} in {layer_name} layer")

        # Ensure HCV_Catego exists and is numeric
        if 'HCV_Catego' in gdf.columns:
            gdf['HCV_Catego'] = pd.to_numeric(gdf['HCV_Catego'], errors='coerce').fillna(0).astype(int)
        else:
            self.log(f"Warning: HCV_Catego column not found in {layer_name} layer")

    def check_crs_compatibility(self):
        """Check and align CRS between layers"""
        if self.gdf_base.crs != self.gdf_cutting.crs:
            self.log(f"CRS mismatch detected:")
            self.log(f"  Base layer CRS: {self.gdf_base.crs}")
            self.log(f"  Cutting layer CRS: {self.gdf_cutting.crs}")
            self.log("Converting cutting layer to match base layer CRS...")

            self.gdf_cutting = self.gdf_cutting.to_crs(self.gdf_base.crs)
            self.log("CRS alignment completed")
        else:
            self.log("CRS compatibility confirmed")

    def populate_feature_lists(self):
        """Populate feature selection listboxes"""
        # Clear existing items
        self.base_listbox.delete(0, tk.END)
        self.cutting_listbox.delete(0, tk.END)

        # Filter features with HCV_Catego = 0
        if 'HCV_Catego' in self.gdf_base.columns:
            base_filtered = self.gdf_base[self.gdf_base['HCV_Catego'] == 0]
        else:
            base_filtered = self.gdf_base  # Use all features if no HCV_Catego

        if 'HCV_Catego' in self.gdf_cutting.columns:
            cutting_filtered = self.gdf_cutting[self.gdf_cutting['HCV_Catego'] == 0]
        else:
            cutting_filtered = self.gdf_cutting  # Use all features if no HCV_Catego

        # Populate base layer features
        self.base_features = []
        for idx, row in base_filtered.iterrows():
            feature_info = self.get_feature_info(row, idx)
            self.base_features.append((idx, feature_info))
            self.base_listbox.insert(tk.END, feature_info)

        # Populate cutting layer features
        self.cutting_features = []
        for idx, row in cutting_filtered.iterrows():
            feature_info = self.get_feature_info(row, idx)
            self.cutting_features.append((idx, feature_info))
            self.cutting_listbox.insert(tk.END, feature_info)

        self.log(f"Base layer: {len(self.base_features)} features with HCV_Catego=0")
        self.log(f"Cutting layer: {len(self.cutting_features)} features with HCV_Catego=0")

    def get_feature_info(self, row, idx):
        """Get descriptive info for a feature"""
        info_parts = [f"ID: {idx}"]

        # Add BLOK and SUBDIVISI if available
        if 'BLOK' in row.index and pd.notna(row['BLOK']):
            info_parts.append(f"BLOK: {row['BLOK']}")
        if 'SUBDIVISI' in row.index and pd.notna(row['SUBDIVISI']):
            info_parts.append(f"SUB: {row['SUBDIVISI']}")

        # Add area if geometry is available
        if hasattr(row, 'geometry') and row.geometry is not None:
            area_ha = row.geometry.area / 10000  # Convert to hectares
            info_parts.append(f"Area: {area_ha:.2f} ha")

        return " | ".join(info_parts)

    def update_layer_info(self):
        """Update layer information labels"""
        if self.gdf_base is not None:
            base_info = f"Features: {len(self.gdf_base)} | CRS: {self.gdf_base.crs}"
            self.base_info_var.set(base_info)

        if self.gdf_cutting is not None:
            cutting_info = f"Features: {len(self.gdf_cutting)} | CRS: {self.gdf_cutting.crs}"
            self.cutting_info_var.set(cutting_info)

    def load_layers(self):
        """Load both layers and populate feature lists"""
        try:
            if not self.base_path_var.get() or not self.cutting_path_var.get():
                messagebox.showerror("Error", "Please select both base and cutting layer files")
                return

            self.status_var.set("Loading layers...")
            self.progress_var.set(10)

            # Load base layer
            self.log("Loading base layer...")
            self.gdf_base = gpd.read_file(self.base_path_var.get())
            self.log(f"Base layer loaded: {len(self.gdf_base)} features")

            # Standardize columns
            self.standardize_columns(self.gdf_base, "base")

            self.progress_var.set(30)

            # Load cutting layer
            self.log("Loading cutting layer...")
            self.gdf_cutting = gpd.read_file(self.cutting_path_var.get())
            self.log(f"Cutting layer loaded: {len(self.gdf_cutting)} features")

            # Standardize columns
            self.standardize_columns(self.gdf_cutting, "cutting")

            self.progress_var.set(50)

            # Check CRS compatibility
            self.check_crs_compatibility()

            self.progress_var.set(70)

            # Populate feature lists
            self.populate_feature_lists()

            self.progress_var.set(100)
            self.status_var.set("Layers loaded successfully")

            # Update info labels
            self.update_layer_info()

        except Exception as e:
            self.log(f"Error loading layers: {str(e)}")
            messagebox.showerror("Error", f"Failed to load layers: {str(e)}")
            self.status_var.set("Error loading layers")
            self.progress_var.set(0)

    def standardize_columns(self, gdf, layer_name):
        """Standardize column names"""
        column_mappings = {
            'SUB_DIVISI': 'SUBDIVISI',
            'HCV_Catego': 'HCV_Catego',
            'Jumlah_Poh': 'JUMLAH_POH'
        }

        for old, new in column_mappings.items():
            if old in gdf.columns and new not in gdf.columns:
                gdf.rename(columns={old: new}, inplace=True)
                self.log(f"Renamed column {old} to {new} in {layer_name} layer")

        # Ensure HCV_Catego exists and is numeric
        if 'HCV_Catego' in gdf.columns:
            gdf['HCV_Catego'] = pd.to_numeric(gdf['HCV_Catego'], errors='coerce').fillna(0).astype(int)
        else:
            self.log(f"Warning: HCV_Catego column not found in {layer_name} layer")

    def check_crs_compatibility(self):
        """Check and align CRS between layers"""
        if self.gdf_base.crs != self.gdf_cutting.crs:
            self.log(f"CRS mismatch detected:")
            self.log(f"  Base layer CRS: {self.gdf_base.crs}")
            self.log(f"  Cutting layer CRS: {self.gdf_cutting.crs}")
            self.log("Converting cutting layer to match base layer CRS...")

            self.gdf_cutting = self.gdf_cutting.to_crs(self.gdf_base.crs)
            self.log("CRS alignment completed")
        else:
            self.log("CRS compatibility confirmed")

    def populate_feature_lists(self):
        """Populate feature selection listboxes"""
        # Clear existing items
        self.base_listbox.delete(0, tk.END)
        self.cutting_listbox.delete(0, tk.END)

        # Filter features with HCV_Catego = 0
        if 'HCV_Catego' in self.gdf_base.columns:
            base_filtered = self.gdf_base[self.gdf_base['HCV_Catego'] == 0]
        else:
            base_filtered = self.gdf_base  # Use all features if no HCV_Catego

        if 'HCV_Catego' in self.gdf_cutting.columns:
            cutting_filtered = self.gdf_cutting[self.gdf_cutting['HCV_Catego'] == 0]
        else:
            cutting_filtered = self.gdf_cutting  # Use all features if no HCV_Catego

        # Populate base layer features
        self.base_features = []
        for idx, row in base_filtered.iterrows():
            feature_info = self.get_feature_info(row, idx)
            self.base_features.append((idx, feature_info))
            self.base_listbox.insert(tk.END, feature_info)

        # Populate cutting layer features
        self.cutting_features = []
        for idx, row in cutting_filtered.iterrows():
            feature_info = self.get_feature_info(row, idx)
            self.cutting_features.append((idx, feature_info))
            self.cutting_listbox.insert(tk.END, feature_info)

        self.log(f"Base layer: {len(self.base_features)} features with HCV_Catego=0")
        self.log(f"Cutting layer: {len(self.cutting_features)} features with HCV_Catego=0")

    def get_feature_info(self, row, idx):
        """Get descriptive info for a feature"""
        info_parts = [f"ID: {idx}"]

        # Add BLOK and SUBDIVISI if available
        if 'BLOK' in row.index and pd.notna(row['BLOK']):
            info_parts.append(f"BLOK: {row['BLOK']}")
        if 'SUBDIVISI' in row.index and pd.notna(row['SUBDIVISI']):
            info_parts.append(f"SUB: {row['SUBDIVISI']}")

        # Add area if geometry is available
        if hasattr(row, 'geometry') and row.geometry is not None:
            area_ha = row.geometry.area / 10000  # Convert to hectares
            info_parts.append(f"Area: {area_ha:.2f} ha")

        return " | ".join(info_parts)

    def update_layer_info(self):
        """Update layer information labels"""
        if self.gdf_base is not None:
            base_info = f"Features: {len(self.gdf_base)} | CRS: {self.gdf_base.crs}"
            self.base_info_var.set(base_info)

        if self.gdf_cutting is not None:
            cutting_info = f"Features: {len(self.gdf_cutting)} | CRS: {self.gdf_cutting.crs}"
            self.cutting_info_var.set(cutting_info)

    def process_difference(self):
        """Process spatial difference operation"""
        try:
            # Validate inputs
            if not self.validate_inputs():
                return

            self.status_var.set("Processing difference operation...")
            self.progress_var.set(0)

            # Get selected features
            base_selected = self.get_selected_features(self.base_listbox, self.base_features, "base")
            cutting_selected = self.get_selected_features(self.cutting_listbox, self.cutting_features, "cutting")

            if not base_selected or not cutting_selected:
                messagebox.showerror("Error", "Please select at least one feature from each layer")
                return

            self.log(f"Selected {len(base_selected)} base features and {len(cutting_selected)} cutting features")
            self.progress_var.set(20)

            # Perform difference operation
            result_features = []
            total_operations = len(base_selected)

            for i, base_idx in enumerate(base_selected):
                if i % 5 == 0:  # Update progress every 5 operations
                    progress = 20 + (i / total_operations) * 60
                    self.progress_var.set(progress)
                    self.root.update_idletasks()

                base_geom = self.gdf_base.loc[base_idx, 'geometry']
                base_row = self.gdf_base.loc[base_idx].copy()

                # Apply difference with all cutting features
                result_geom = base_geom
                cuts_applied = 0

                for cutting_idx in cutting_selected:
                    cutting_geom = self.gdf_cutting.loc[cutting_idx, 'geometry']

                    # Check if geometries intersect
                    if result_geom.intersects(cutting_geom):
                        try:
                            result_geom = result_geom.difference(cutting_geom)
                            cuts_applied += 1
                            self.log(f"Applied cut from feature {cutting_idx} to base feature {base_idx}")
                        except Exception as e:
                            self.log(f"Warning: Could not apply cut from {cutting_idx} to {base_idx}: {str(e)}")

                # Only keep result if it's still a valid geometry
                if not result_geom.is_empty and result_geom.geom_type in ['Polygon', 'MultiPolygon']:
                    base_row['geometry'] = result_geom
                    base_row['cuts_applied'] = cuts_applied
                    base_row['original_area'] = base_geom.area / 10000
                    base_row['result_area'] = result_geom.area / 10000
                    base_row['area_removed'] = base_row['original_area'] - base_row['result_area']
                    result_features.append(base_row)
                    self.log(f"Base feature {base_idx}: {cuts_applied} cuts applied, area reduced by {base_row['area_removed']:.2f} ha")
                else:
                    self.log(f"Warning: Base feature {base_idx} resulted in empty or invalid geometry after cuts")

            self.progress_var.set(80)

            if not result_features:
                messagebox.showerror("Error", "No valid results after difference operation")
                self.status_var.set("No valid results")
                return

            # Create result GeoDataFrame
            result_gdf = gpd.GeoDataFrame(result_features, crs=self.gdf_base.crs)
            self.log(f"Created result with {len(result_gdf)} features")

            self.progress_var.set(90)

            # Save result
            self.save_result(result_gdf)

            self.progress_var.set(100)
            self.status_var.set("Difference operation completed successfully")

            # Show summary
            self.show_result_summary(result_gdf)

        except Exception as e:
            self.log(f"Error in difference operation: {str(e)}")
            messagebox.showerror("Error", f"Failed to process difference: {str(e)}")
            self.status_var.set("Error in processing")
            self.progress_var.set(0)

    def validate_inputs(self):
        """Validate all required inputs"""
        if self.gdf_base is None or self.gdf_cutting is None:
            messagebox.showerror("Error", "Please load both layers first")
            return False

        if not self.output_path_var.get():
            messagebox.showerror("Error", "Please specify output file location")
            return False

        return True

    def get_selected_features(self, listbox, features_list, layer_name):
        """Get indices of selected features from listbox"""
        selected_indices = listbox.curselection()
        if not selected_indices:
            return []

        selected_features = []
        for i in selected_indices:
            feature_idx = features_list[i][0]  # Get the original index
            selected_features.append(feature_idx)

        self.log(f"Selected {len(selected_features)} features from {layer_name} layer")
        return selected_features

    def save_result(self, result_gdf):
        """Save result GeoDataFrame to shapefile"""
        output_path = self.output_path_var.get()

        try:
            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Save to shapefile
            result_gdf.to_file(output_path)
            self.log(f"Result saved to: {output_path}")

            # Also save summary to CSV
            csv_path = output_path.replace('.shp', '_summary.csv')
            summary_df = result_gdf.drop('geometry', axis=1)
            summary_df.to_csv(csv_path, index=False)
            self.log(f"Summary saved to: {csv_path}")

        except Exception as e:
            raise Exception(f"Failed to save result: {str(e)}")

    def show_result_summary(self, result_gdf):
        """Show summary of results"""
        total_features = len(result_gdf)
        total_original_area = result_gdf['original_area'].sum()
        total_result_area = result_gdf['result_area'].sum()
        total_area_removed = result_gdf['area_removed'].sum()

        summary_msg = f"""Difference Operation Summary:

Total Features Processed: {total_features}
Original Total Area: {total_original_area:.2f} ha
Result Total Area: {total_result_area:.2f} ha
Total Area Removed: {total_area_removed:.2f} ha
Area Reduction: {(total_area_removed/total_original_area)*100:.1f}%

Results saved to: {self.output_path_var.get()}"""

        messagebox.showinfo("Operation Complete", summary_msg)
        self.log("=== OPERATION SUMMARY ===")
        self.log(f"Total features processed: {total_features}")
        self.log(f"Total area removed: {total_area_removed:.2f} ha")

    def clear_all(self):
        """Clear all data and reset interface"""
        # Clear file paths
        self.base_path_var.set("")
        self.cutting_path_var.set("")
        self.output_path_var.set("")

        # Clear data
        self.gdf_base = None
        self.gdf_cutting = None
        self.base_features = []
        self.cutting_features = []

        # Clear listboxes
        self.base_listbox.delete(0, tk.END)
        self.cutting_listbox.delete(0, tk.END)

        # Reset info labels
        self.base_info_var.set("No file selected")
        self.cutting_info_var.set("No file selected")

        # Reset progress and status
        self.progress_var.set(0)
        self.status_var.set("Ready")

        # Clear log
        self.log_text.delete(1.0, tk.END)

        self.log("All data cleared")

def main():
    """Main function to run the application"""
    root = tk.Tk()
    app = DifferenceFeatureGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
