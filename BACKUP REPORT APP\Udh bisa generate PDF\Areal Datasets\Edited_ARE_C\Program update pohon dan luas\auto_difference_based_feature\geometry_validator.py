#!/usr/bin/env python3
"""
Geometry Validator - Comprehensive geometry validation and repair utilities
Handles invalid geometries, topology issues, and geometric corrections.

Author: Augment Agent
Date: 2025-07-05
"""

import geopandas as gpd
import pandas as pd
from shapely.geometry import Polygon, MultiPolygon, Point, LineString, GeometryCollection
from shapely.validation import make_valid
from shapely.ops import unary_union
import logging
from typing import Tuple, List, Optional
import warnings

class GeometryValidator:
    """Comprehensive geometry validation and repair utilities"""
    
    def __init__(self, logger=None):
        """Initialize the geometry validator"""
        self.logger = logger or logging.getLogger(__name__)
        self.repair_stats = {
            'invalid_fixed': 0,
            'geometry_collections_fixed': 0,
            'multipolygons_simplified': 0,
            'null_geometries_removed': 0,
            'topology_errors_fixed': 0
        }
    
    def validate_and_repair_geodataframe(self, gdf: gpd.GeoDataFrame) -> gpd.GeoDataFrame:
        """
        Comprehensive validation and repair of all geometries in a GeoDataFrame
        
        Args:
            gdf: Input GeoDataFrame
            
        Returns:
            GeoDataFrame with repaired geometries
        """
        self.logger.info("Starting comprehensive geometry validation and repair...")
        
        # Reset repair statistics
        self.repair_stats = {key: 0 for key in self.repair_stats}
        
        # Create a copy for processing
        gdf_repaired = gdf.copy()
        
        # Step 1: Remove null geometries
        gdf_repaired = self._remove_null_geometries(gdf_repaired)
        
        # Step 2: Fix invalid geometries
        gdf_repaired = self._fix_invalid_geometries(gdf_repaired)
        
        # Step 3: Handle GeometryCollections
        gdf_repaired = self._fix_geometry_collections(gdf_repaired)
        
        # Step 4: Simplify MultiPolygons
        gdf_repaired = self._simplify_multipolygons(gdf_repaired)
        
        # Step 5: Final validation
        gdf_repaired = self._final_validation(gdf_repaired)
        
        # Log repair statistics
        self._log_repair_statistics()
        
        return gdf_repaired
    
    def _remove_null_geometries(self, gdf: gpd.GeoDataFrame) -> gpd.GeoDataFrame:
        """Remove features with null or empty geometries"""
        initial_count = len(gdf)
        
        # Remove null geometries
        gdf_clean = gdf[gdf.geometry.notna()].copy()
        
        # Remove empty geometries
        gdf_clean = gdf_clean[~gdf_clean.geometry.is_empty].copy()
        
        removed_count = initial_count - len(gdf_clean)
        if removed_count > 0:
            self.repair_stats['null_geometries_removed'] = removed_count
            self.logger.warning(f"Removed {removed_count} null/empty geometries")
        
        return gdf_clean
    
    def _fix_invalid_geometries(self, gdf: gpd.GeoDataFrame) -> gpd.GeoDataFrame:
        """Fix invalid geometries using Shapely's make_valid function"""
        invalid_mask = ~gdf.geometry.is_valid
        invalid_count = invalid_mask.sum()
        
        if invalid_count > 0:
            self.logger.info(f"Fixing {invalid_count} invalid geometries...")
            
            for idx in gdf[invalid_mask].index:
                try:
                    original_geom = gdf.at[idx, 'geometry']
                    
                    # Try buffer(0) first (faster)
                    fixed_geom = original_geom.buffer(0)
                    
                    # If still invalid, use make_valid
                    if not fixed_geom.is_valid:
                        fixed_geom = make_valid(original_geom)
                    
                    gdf.at[idx, 'geometry'] = fixed_geom
                    self.repair_stats['invalid_fixed'] += 1
                    
                except Exception as e:
                    self.logger.warning(f"Could not fix invalid geometry at index {idx}: {e}")
                    continue
        
        return gdf
    
    def _fix_geometry_collections(self, gdf: gpd.GeoDataFrame) -> gpd.GeoDataFrame:
        """Convert GeometryCollections to appropriate geometry types"""
        collection_mask = gdf.geometry.geom_type == 'GeometryCollection'
        collection_count = collection_mask.sum()
        
        if collection_count > 0:
            self.logger.info(f"Fixing {collection_count} GeometryCollections...")
            
            for idx in gdf[collection_mask].index:
                try:
                    geom_collection = gdf.at[idx, 'geometry']
                    
                    # Extract polygons from the collection
                    polygons = [g for g in geom_collection.geoms 
                              if g.geom_type in ['Polygon', 'MultiPolygon']]
                    
                    if polygons:
                        if len(polygons) == 1:
                            fixed_geom = polygons[0]
                        else:
                            # Union multiple polygons
                            fixed_geom = unary_union(polygons)
                        
                        # If result is MultiPolygon, take the largest
                        if fixed_geom.geom_type == 'MultiPolygon':
                            fixed_geom = max(fixed_geom.geoms, key=lambda x: x.area)
                        
                        gdf.at[idx, 'geometry'] = fixed_geom
                        self.repair_stats['geometry_collections_fixed'] += 1
                    
                except Exception as e:
                    self.logger.warning(f"Could not fix GeometryCollection at index {idx}: {e}")
                    continue
        
        return gdf
    
    def _simplify_multipolygons(self, gdf: gpd.GeoDataFrame) -> gpd.GeoDataFrame:
        """Simplify MultiPolygons by keeping only the largest polygon"""
        multipolygon_mask = gdf.geometry.geom_type == 'MultiPolygon'
        multipolygon_count = multipolygon_mask.sum()
        
        if multipolygon_count > 0:
            self.logger.info(f"Simplifying {multipolygon_count} MultiPolygons...")
            
            for idx in gdf[multipolygon_mask].index:
                try:
                    multipolygon = gdf.at[idx, 'geometry']
                    
                    # Keep the largest polygon
                    largest_polygon = max(multipolygon.geoms, key=lambda x: x.area)
                    
                    gdf.at[idx, 'geometry'] = largest_polygon
                    self.repair_stats['multipolygons_simplified'] += 1
                    
                except Exception as e:
                    self.logger.warning(f"Could not simplify MultiPolygon at index {idx}: {e}")
                    continue
        
        return gdf
    
    def _final_validation(self, gdf: gpd.GeoDataFrame) -> gpd.GeoDataFrame:
        """Final validation and cleanup"""
        # Check for any remaining invalid geometries
        invalid_mask = ~gdf.geometry.is_valid
        invalid_count = invalid_mask.sum()
        
        if invalid_count > 0:
            self.logger.warning(f"Still have {invalid_count} invalid geometries after repair")
            
            # Try one more repair attempt
            for idx in gdf[invalid_mask].index:
                try:
                    geom = gdf.at[idx, 'geometry']
                    # More aggressive repair
                    fixed_geom = geom.buffer(0.001).buffer(-0.001)
                    if fixed_geom.is_valid and not fixed_geom.is_empty:
                        gdf.at[idx, 'geometry'] = fixed_geom
                        self.repair_stats['topology_errors_fixed'] += 1
                except:
                    continue
        
        # Remove any geometries that are still invalid or empty
        final_invalid_mask = ~gdf.geometry.is_valid | gdf.geometry.is_empty
        if final_invalid_mask.any():
            removed_count = final_invalid_mask.sum()
            gdf = gdf[~final_invalid_mask].copy()
            self.logger.warning(f"Removed {removed_count} geometries that could not be repaired")
        
        return gdf
    
    def _log_repair_statistics(self):
        """Log comprehensive repair statistics"""
        self.logger.info("=== GEOMETRY REPAIR STATISTICS ===")
        for stat_name, count in self.repair_stats.items():
            if count > 0:
                self.logger.info(f"  {stat_name.replace('_', ' ').title()}: {count}")
        
        total_repairs = sum(self.repair_stats.values())
        self.logger.info(f"  Total Repairs: {total_repairs}")
    
    def check_geometry_validity(self, gdf: gpd.GeoDataFrame) -> dict:
        """
        Check geometry validity and return detailed statistics
        
        Returns:
            Dictionary with validity statistics
        """
        stats = {
            'total_features': len(gdf),
            'valid_geometries': 0,
            'invalid_geometries': 0,
            'null_geometries': 0,
            'empty_geometries': 0,
            'geometry_types': {},
            'invalid_reasons': []
        }
        
        for idx, geom in enumerate(gdf.geometry):
            if geom is None:
                stats['null_geometries'] += 1
            elif geom.is_empty:
                stats['empty_geometries'] += 1
            elif geom.is_valid:
                stats['valid_geometries'] += 1
                geom_type = geom.geom_type
                stats['geometry_types'][geom_type] = stats['geometry_types'].get(geom_type, 0) + 1
            else:
                stats['invalid_geometries'] += 1
                try:
                    from shapely.validation import explain_validity
                    reason = explain_validity(geom)
                    if reason not in stats['invalid_reasons']:
                        stats['invalid_reasons'].append(reason)
                except:
                    pass
        
        return stats
    
    def get_repair_summary(self) -> str:
        """Get a formatted summary of repair operations"""
        total_repairs = sum(self.repair_stats.values())
        
        if total_repairs == 0:
            return "No geometry repairs were needed."
        
        summary_lines = ["Geometry Repair Summary:"]
        for stat_name, count in self.repair_stats.items():
            if count > 0:
                formatted_name = stat_name.replace('_', ' ').title()
                summary_lines.append(f"  • {formatted_name}: {count}")
        
        summary_lines.append(f"  • Total Repairs: {total_repairs}")
        
        return "\n".join(summary_lines)
