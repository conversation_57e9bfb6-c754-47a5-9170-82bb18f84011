#!/usr/bin/env python3
"""
Test script untuk Difference Feature GUI
Menguji import dan basic functionality
"""

import sys
import os

def test_imports():
    """Test semua import yang diperlukan"""
    print("Testing imports...")
    
    try:
        import tkinter as tk
        print("✓ tkinter imported successfully")
    except ImportError as e:
        print(f"✗ tkinter import failed: {e}")
        return False
    
    try:
        import geopandas as gpd
        print("✓ geopandas imported successfully")
    except ImportError as e:
        print(f"✗ geopandas import failed: {e}")
        return False
    
    try:
        import pandas as pd
        print("✓ pandas imported successfully")
    except ImportError as e:
        print(f"✗ pandas import failed: {e}")
        return False
    
    try:
        from pathlib import Path
        print("✓ pathlib imported successfully")
    except ImportError as e:
        print(f"✗ pathlib import failed: {e}")
        return False
    
    try:
        import logging
        print("✓ logging imported successfully")
    except ImportError as e:
        print(f"✗ logging import failed: {e}")
        return False
    
    return True

def test_gui_creation():
    """Test pembuatan GUI tanpa menampilkan"""
    print("\nTesting GUI creation...")
    
    try:
        # Import main class
        from difference_feature_gui import DifferenceFeatureGUI
        print("✓ DifferenceFeatureGUI class imported successfully")
        
        # Create root window (hidden)
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        # Create GUI instance
        app = DifferenceFeatureGUI(root)
        print("✓ DifferenceFeatureGUI instance created successfully")
        
        # Test some basic attributes
        if hasattr(app, 'gdf_base'):
            print("✓ gdf_base attribute exists")
        if hasattr(app, 'gdf_cutting'):
            print("✓ gdf_cutting attribute exists")
        if hasattr(app, 'log'):
            print("✓ log method exists")
        
        # Cleanup
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ GUI creation failed: {e}")
        return False

def test_file_structure():
    """Test struktur file yang diperlukan"""
    print("\nTesting file structure...")
    
    required_files = [
        'difference_feature_gui.py',
        'README.md',
        'requirements.txt',
        'run_difference_gui.bat'
    ]
    
    all_exist = True
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file} exists")
        else:
            print(f"✗ {file} missing")
            all_exist = False
    
    return all_exist

def main():
    """Main test function"""
    print("=" * 50)
    print("  DIFFERENCE FEATURE GUI - TEST SUITE")
    print("=" * 50)
    
    # Test 1: Imports
    imports_ok = test_imports()
    
    # Test 2: File structure
    files_ok = test_file_structure()
    
    # Test 3: GUI creation (only if imports are OK)
    gui_ok = False
    if imports_ok:
        gui_ok = test_gui_creation()
    else:
        print("\nSkipping GUI test due to import failures")
    
    # Summary
    print("\n" + "=" * 50)
    print("  TEST SUMMARY")
    print("=" * 50)
    print(f"Imports:        {'PASS' if imports_ok else 'FAIL'}")
    print(f"File Structure: {'PASS' if files_ok else 'FAIL'}")
    print(f"GUI Creation:   {'PASS' if gui_ok else 'FAIL'}")
    
    if imports_ok and files_ok and gui_ok:
        print("\n🎉 All tests PASSED! Program ready to use.")
        return 0
    else:
        print("\n❌ Some tests FAILED. Check errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
