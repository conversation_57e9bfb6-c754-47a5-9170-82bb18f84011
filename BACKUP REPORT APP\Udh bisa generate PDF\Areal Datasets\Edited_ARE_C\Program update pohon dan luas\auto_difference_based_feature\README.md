# Difference Feature GUI - Fitur Pembolong

Program GUI untuk melakukan operasi spatial difference (pembolong) antara dua layer shapefile berdasarkan feature yang dipilih.

## Fitur Utama

### 1. Input Layer
- **Base Layer**: Layer yang akan dipotong/dibolong
- **Cutting Layer**: Layer yang digunakan untuk memotong

### 2. Filter Otomatis
- Otomatis memfilter feature dengan `HCV_Catego = 0` dari kedua layer
- Menampilkan informasi feature (ID, BLOK, SUBDIVISI, Area)

### 3. Feature Selection
- Pilih multiple features dari base layer
- Pilih multiple features dari cutting layer
- Interface listbox yang mudah digunakan

### 4. Spatial Operations
- Operasi difference (pembolong) menggunakan GeoPandas
- Otomatis menangani CRS compatibility
- Validasi geometri hasil

### 5. Output
- Simpan hasil ke shapefile baru
- Export summary ke CSV
- Informasi statistik area (original, result, removed)

## Cara Penggunaan

### 1. Persiapan Data
- Pastikan kedua shapefile memiliki kolom `HCV_Catego`
- Data harus dalam format shapefile (.shp)

### 2. Langkah Operasi
1. **Load Layers**:
   - <PERSON><PERSON> "Browse" untuk Base Layer
   - Klik "Browse" untuk Cutting Layer
   - Klik "Load Layers" untuk memuat data

2. **Select Features**:
   - Pilih feature dari Base Layer (yang akan dipotong)
   - Pilih feature dari Cutting Layer (pemotong)
   - Bisa pilih multiple features dengan Ctrl+Click

3. **Set Output**:
   - Klik "Browse" di bagian Output
   - Tentukan lokasi dan nama file hasil

4. **Process**:
   - Klik "Process Difference"
   - Tunggu hingga selesai
   - Lihat summary hasil

### 3. Hasil Output
- **Shapefile**: File hasil dengan geometri yang sudah dipotong
- **CSV Summary**: Ringkasan statistik area
- **Log**: Detail proses operasi

## Struktur Output

### Kolom Tambahan di Hasil
- `cuts_applied`: Jumlah potongan yang diterapkan
- `original_area`: Luas area asli (ha)
- `result_area`: Luas area hasil (ha)
- `area_removed`: Luas area yang dipotong (ha)

### File Output
```
output_file.shp          # Shapefile hasil
output_file_summary.csv  # Summary statistik
logs/difference_feature_YYYYMMDD_HHMMSS.log  # Log file
```

## Persyaratan Sistem

### Dependencies
```
geopandas
pandas
tkinter (built-in Python)
shapely
```

### Python Version
- Python 3.7+

## Instalasi

1. Pastikan Python dan dependencies terinstall
2. Copy file `difference_feature_gui.py` ke direktori kerja
3. Jalankan dengan: `python difference_feature_gui.py`

## Contoh Penggunaan

### Skenario 1: Memotong Blok dengan Area Terlarang
- Base Layer: Polygon blok perkebunan
- Cutting Layer: Area terlarang/restricted
- Hasil: Blok yang sudah dipotong area terlarang

### Skenario 2: Menghilangkan Overlap
- Base Layer: Boundary utama
- Cutting Layer: Area overlap yang harus dihilangkan
- Hasil: Boundary bersih tanpa overlap

## Troubleshooting

### Error "CRS Mismatch"
- Program otomatis menangani perbedaan CRS
- Cutting layer akan dikonversi ke CRS base layer

### Error "No Valid Results"
- Periksa apakah feature yang dipilih benar-benar bersinggungan
- Pastikan geometri input valid

### Error "Empty Geometry"
- Hasil operasi difference bisa menghasilkan geometri kosong
- Feature dengan hasil kosong tidak disimpan

## Logging

Program mencatat semua aktivitas ke:
- GUI log window (real-time)
- File log di folder `logs/`
- Console output

## Limitasi

1. Hanya mendukung format shapefile
2. Filter otomatis hanya untuk `HCV_Catego = 0`
3. Hasil MultiPolygon dikonversi ke Polygon terbesar
4. Membutuhkan memory sesuai ukuran data

## Support

Untuk pertanyaan atau masalah, periksa:
1. Log file untuk detail error
2. Validasi format data input
3. Pastikan dependencies terinstall dengan benar

---

**Dibuat oleh**: Augment Agent  
**Tanggal**: 2025-07-05  
**Versi**: 1.0
